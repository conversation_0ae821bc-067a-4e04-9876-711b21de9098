#!/usr/bin/env python3
"""
道路结冰预测系统主程序
基于LSTM Encoder-Decoder架构
"""
import os
import sys
import argparse
import json
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils import create_default_config, save_config, set_seed, get_device
from src.data_loader import create_data_loaders
from src.model import create_model, IcePredictionModel
from src.train import Trainer
from src.evaluate import Evaluator


def setup_directories(config):
    """创建必要的目录"""
    directories = [
        config['training']['checkpoint_dir'],
        config['training']['log_dir'],
        config['evaluation']['prediction_dir']
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"创建目录: {directory}")


def print_system_info():
    """打印系统信息"""
    print("=" * 60)
    print("道路结冰预测系统")
    print("基于LSTM Encoder-Decoder架构")
    print("=" * 60)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设备信息
    device = get_device()
    
    # 数据信息
    if os.path.exists('ice_events'):
        csv_files = [f for f in os.listdir('ice_events') if f.endswith('.csv')]
        print(f"数据文件数量: {len(csv_files)}")
    else:
        print("警告: 未找到ice_events数据目录")
    
    print("=" * 60)


def train_model(config_path, resume_path=None):
    """训练模型"""
    print("\n开始训练模型...")
    
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 设置随机种子
    set_seed(42)
    
    # 创建目录
    setup_directories(config)
    
    # 创建训练器
    trainer = Trainer(config)
    
    # 恢复训练（如果指定）
    if resume_path and os.path.exists(resume_path):
        from src.utils import load_checkpoint
        checkpoint = load_checkpoint(resume_path)
        trainer.model.load_state_dict(checkpoint['model_state_dict'])
        trainer.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        trainer.current_epoch = checkpoint['epoch']
        trainer.best_val_loss = checkpoint['loss']
        print(f'从第 {trainer.current_epoch + 1} 轮恢复训练')
    
    # 开始训练
    trainer.train()


def evaluate_model(config_path, model_path, threshold=None):
    """评估模型"""
    print(f"\n开始评估模型: {model_path}")
    
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 更新阈值（如果指定）
    if threshold is not None:
        config['evaluation']['threshold'] = threshold
        print(f"使用自定义阈值: {threshold}")
    
    # 创建评估器
    evaluator = Evaluator(config, model_path)
    
    # 运行评估
    overall_metrics, timestep_metrics, predictions, targets = evaluator.run_evaluation()

    return overall_metrics, timestep_metrics, predictions, targets


def test_data_loading(config_path):
    """测试数据加载"""
    print("\n测试数据加载...")
    
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    try:
        # 创建数据加载器
        train_loader, test_loader = create_data_loaders(
            data_dir=config['data']['data_dir'],
            batch_size=config['training']['batch_size'],
            sequence_length=config['data']['sequence_length'],
            prediction_horizon=config['data']['prediction_horizon'],
            num_workers=config['data']['num_workers'],
            sample_ratio=config['data'].get('sample_ratio', 1.0),
            random_seed=config['data'].get('random_seed', 42),
            train_ratio=config['data'].get('train_ratio', 0.8)
        )

        print(f"✓ 训练集批次数: {len(train_loader)}")
        print(f"✓ 测试集批次数: {len(test_loader)}")
        
        # 测试一个批次
        for batch in train_loader:
            print("\n批次数据形状:")
            for key, value in batch.items():
                if hasattr(value, 'shape'):
                    print(f"  {key}: {value.shape}")
                else:
                    print(f"  {key}: {type(value)}")
            break
        
        print("✓ 数据加载测试成功")
        
    except Exception as e:
        print(f"✗ 数据加载测试失败: {e}")


def test_model(config_path):
    """测试模型创建"""
    print("\n测试模型创建...")
    
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    try:
        # 创建模型
        model = create_model(config['model'])
        
        # 计算参数数量
        from src.utils import count_parameters
        total_params, trainable_params = count_parameters(model)
        
        print(f"✓ 模型创建成功")
        print(f"✓ 总参数数量: {total_params:,}")
        print(f"✓ 可训练参数数量: {trainable_params:,}")
        
        # 测试前向传播
        import torch
        batch_size = 4
        seq_len = config['data']['sequence_length']
        pred_len = config['data']['prediction_horizon']
        
        weather_history = torch.randn(batch_size, seq_len, config['model']['weather_input_dim'])
        weather_future = torch.randn(batch_size, pred_len, config['model']['weather_input_dim'])
        road_history = torch.randn(batch_size, seq_len, config['model']['road_input_dim'])
        
        with torch.no_grad():
            predictions = model(weather_history, weather_future, road_history, pred_len)
            print(f"✓ 前向传播测试成功，输出形状: {predictions.shape}")
        
    except Exception as e:
        print(f"✗ 模型测试失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='道路结冰预测系统')
    parser.add_argument('command', choices=['train', 'evaluate', 'test-data', 'test-model', 'create-config'],
                       help='执行的命令')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--model', type=str, help='模型检查点路径（用于评估）')
    parser.add_argument('--resume', type=str, help='恢复训练的检查点路径')
    parser.add_argument('--threshold', type=float, help='评估时的分类阈值')
    
    args = parser.parse_args()
    
    # 打印系统信息
    print_system_info()
    
    if args.command == 'create-config':
        # 创建默认配置文件
        config = create_default_config()
        save_config(config, args.config)
        print(f"✓ 已创建配置文件: {args.config}")
        
    elif args.command == 'test-data':
        # 测试数据加载
        if not os.path.exists(args.config):
            print(f"✗ 配置文件不存在: {args.config}")
            print("请先运行: python main.py create-config")
            return
        test_data_loading(args.config)
        
    elif args.command == 'test-model':
        # 测试模型创建
        if not os.path.exists(args.config):
            print(f"✗ 配置文件不存在: {args.config}")
            print("请先运行: python main.py create-config")
            return
        test_model(args.config)
        
    elif args.command == 'train':
        # 训练模型
        if not os.path.exists(args.config):
            print(f"✗ 配置文件不存在: {args.config}")
            print("请先运行: python main.py create-config")
            return
        train_model(args.config, args.resume)
        
    elif args.command == 'evaluate':
        # 评估模型
        if not args.model:
            print("✗ 请指定模型路径: --model <model_path>")
            return
        if not os.path.exists(args.model):
            print(f"✗ 模型文件不存在: {args.model}")
            return
        if not os.path.exists(args.config):
            print(f"✗ 配置文件不存在: {args.config}")
            return
        evaluate_model(args.config, args.model, args.threshold)


if __name__ == "__main__":
    main()
