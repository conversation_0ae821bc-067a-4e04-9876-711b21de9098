#!/usr/bin/env python3
"""
道路结冰预测可视化示例脚本
演示如何使用可视化工具
"""
import os
import sys

def main():
    """主函数，演示可视化工具的使用"""
    
    print("=== 道路结冰预测可视化示例 ===\n")
    
    # 检查必要文件是否存在
    config_file = "config.json"
    model_file = "checkpoints/best_model.pth"  # 假设的模型路径
    
    print("1. 检查必要文件...")
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        print("   请先运行: python main.py create-config")
        return
    else:
        print(f"✅ 配置文件存在: {config_file}")
    
    if not os.path.exists(model_file):
        print(f"❌ 模型文件不存在: {model_file}")
        print("   请先训练模型: python main.py train")
        print("   或指定正确的模型路径")
        return
    else:
        print(f"✅ 模型文件存在: {model_file}")
    
    print("\n2. 可视化选项:")
    print("   a) 详细预测结果可视化 (visualize_predictions.py)")
    print("   b) 结冰时段对比可视化 (visualize_ice_periods.py)")
    
    # 获取用户选择
    choice = input("\n请选择可视化类型 (a/b) 或按 Enter 跳过演示: ").strip().lower()
    
    if choice == 'a':
        demonstrate_prediction_visualization(model_file)
    elif choice == 'b':
        demonstrate_period_visualization(model_file)
    else:
        print("跳过演示，显示使用说明...")
        show_usage_instructions()

def demonstrate_prediction_visualization(model_file):
    """演示详细预测结果可视化"""
    print("\n=== 详细预测结果可视化演示 ===")
    
    # 检查数据目录
    data_dir = "ice_events"
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return
    
    # 获取第一个CSV文件作为示例
    csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
    if not csv_files:
        print(f"❌ 数据目录中没有CSV文件: {data_dir}")
        return
    
    example_file = csv_files[0]
    print(f"使用示例文件: {example_file}")
    
    # 构建命令
    commands = [
        f"# 可视化单个文件",
        f"python visualize_predictions.py --model {model_file} --file {example_file}",
        f"",
        f"# 可视化所有文件",
        f"python visualize_predictions.py --model {model_file}",
        f"",
        f"# 自定义输出目录",
        f"python visualize_predictions.py --model {model_file} --output my_predictions",
    ]
    
    print("\n推荐命令:")
    for cmd in commands:
        print(cmd)
    
    # 询问是否执行
    execute = input(f"\n是否执行单个文件可视化? (y/n): ").strip().lower()
    if execute == 'y':
        cmd = f"python visualize_predictions.py --model {model_file} --file {example_file}"
        print(f"执行命令: {cmd}")
        os.system(cmd)

def demonstrate_period_visualization(model_file):
    """演示结冰时段对比可视化"""
    print("\n=== 结冰时段对比可视化演示 ===")
    
    # 检查数据目录
    data_dir = "ice_events"
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return
    
    # 获取第一个CSV文件作为示例
    csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
    if not csv_files:
        print(f"❌ 数据目录中没有CSV文件: {data_dir}")
        return
    
    example_file = csv_files[0]
    print(f"使用示例文件: {example_file}")
    
    # 构建命令
    commands = [
        f"# 可视化单个文件 (默认阈值 0.5)",
        f"python visualize_ice_periods.py --model {model_file} --file {example_file}",
        f"",
        f"# 使用自定义阈值",
        f"python visualize_ice_periods.py --model {model_file} --file {example_file} --threshold 0.3",
        f"",
        f"# 可视化所有文件",
        f"python visualize_ice_periods.py --model {model_file} --threshold 0.4",
        f"",
        f"# 自定义输出目录和阈值",
        f"python visualize_ice_periods.py --model {model_file} --output ice_analysis --threshold 0.6",
    ]
    
    print("\n推荐命令:")
    for cmd in commands:
        print(cmd)
    
    # 询问是否执行
    threshold = input(f"\n请输入预测阈值 (0.1-0.9, 默认0.5): ").strip()
    if not threshold:
        threshold = "0.5"
    
    try:
        threshold_val = float(threshold)
        if 0.1 <= threshold_val <= 0.9:
            cmd = f"python visualize_ice_periods.py --model {model_file} --file {example_file} --threshold {threshold}"
            print(f"执行命令: {cmd}")
            os.system(cmd)
        else:
            print("阈值超出范围，跳过执行")
    except ValueError:
        print("无效的阈值，跳过执行")

def show_usage_instructions():
    """显示使用说明"""
    print("\n=== 可视化工具使用说明 ===")
    
    print("\n1. 详细预测结果可视化 (visualize_predictions.py):")
    print("   - 显示预测概率热力图")
    print("   - 显示实际结冰状态时间序列")
    print("   - 自动标注训练集/测试集")
    
    print("\n   基本用法:")
    print("   python visualize_predictions.py --model checkpoints/best_model.pth --file ice_event_001.csv")
    
    print("\n2. 结冰时段对比可视化 (visualize_ice_periods.py):")
    print("   - 对比实际和预测的结冰时段")
    print("   - 支持自定义预测阈值")
    print("   - 显示结冰时长统计")
    
    print("\n   基本用法:")
    print("   python visualize_ice_periods.py --model checkpoints/best_model.pth --threshold 0.5")
    
    print("\n3. 输出文件:")
    print("   - 详细预测: visualizations/{事件ID}_{数据集类型}_prediction.png")
    print("   - 时段对比: ice_period_visualizations/{事件ID}_{数据集类型}_ice_periods.png")
    
    print("\n4. 常用参数:")
    print("   --model: 模型检查点路径 (必需)")
    print("   --file: 指定单个文件 (可选)")
    print("   --threshold: 预测阈值 (仅时段对比)")
    print("   --output: 输出目录 (可选)")
    print("   --config: 配置文件路径 (默认: config.json)")
    
    print("\n5. 批量处理:")
    print("   不指定 --file 参数即可处理所有文件")
    
    print("\n详细说明请参考: README_VISUALIZATION.md")

if __name__ == "__main__":
    main()
