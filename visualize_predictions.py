#!/usr/bin/env python3
"""
道路结冰预测可视化脚本
根据sequence_length和prediction_horizon对数据进行分段预测并可视化
"""
import os
import sys
import json
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import torch
from typing import Dict, List, Tuple, Optional

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.model import create_model
from src.data_loader import IceDataset
from src.utils import load_checkpoint


class IcePredictionVisualizer:
    """道路结冰预测可视化器"""
    
    def __init__(self, config_path: str, model_path: str):
        """
        初始化可视化器
        
        Args:
            config_path: 配置文件路径
            model_path: 模型检查点路径
        """
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载模型
        self.model = create_model(self.config['model']).to(self.device)
        checkpoint = load_checkpoint(model_path)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        # 获取数据集划分信息
        self.train_events, self.test_events = self._get_event_splits()
        
        print(f"模型加载完成，设备: {self.device}")
        print(f"训练集事件数: {len(self.train_events)}")
        print(f"测试集事件数: {len(self.test_events)}")
    
    def _get_event_splits(self) -> Tuple[List[str], List[str]]:
        """获取训练集和测试集的事件划分"""
        # 创建临时数据集来获取事件划分信息
        train_dataset = IceDataset(
            data_dir=self.config['data']['data_dir'],
            sequence_length=self.config['data']['sequence_length'],
            prediction_horizon=self.config['data']['prediction_horizon'],
            train_ratio=self.config['data'].get('train_ratio', 0.8),
            mode='train',
            sample_ratio=1.0,  # 使用全部数据来获取完整的事件列表
            random_seed=self.config['data'].get('random_seed', 42)
        )
        
        test_dataset = IceDataset(
            data_dir=self.config['data']['data_dir'],
            sequence_length=self.config['data']['sequence_length'],
            prediction_horizon=self.config['data']['prediction_horizon'],
            train_ratio=self.config['data'].get('train_ratio', 0.8),
            mode='test',
            sample_ratio=1.0,
            random_seed=self.config['data'].get('random_seed', 42),
            shared_scalers=(train_dataset.scaler_weather, train_dataset.scaler_road)
        )
        
        return train_dataset.event_ids, test_dataset.event_ids
    
    def predict_single_file(self, event_file: str) -> Tuple[pd.DataFrame, np.ndarray, np.ndarray]:
        """
        对单个文件进行分段预测
        
        Args:
            event_file: 事件文件名（如 'ice_event_001.csv'）
            
        Returns:
            df: 原始数据DataFrame
            predictions: 预测结果数组
            timestamps: 对应的时间戳数组
        """
        # 加载原始数据
        file_path = os.path.join(self.config['data']['data_dir'], event_file)
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['unix_time'], unit='s')
        df = df.sort_values('datetime').reset_index(drop=True)
        
        # 确定事件属于训练集还是测试集
        event_id = event_file.replace('.csv', '')
        is_train = event_id in self.train_events
        
        # 创建对应的数据集来获取标准化器
        if is_train:
            dataset = IceDataset(
                data_dir=self.config['data']['data_dir'],
                sequence_length=self.config['data']['sequence_length'],
                prediction_horizon=self.config['data']['prediction_horizon'],
                train_ratio=self.config['data'].get('train_ratio', 0.8),
                mode='train',
                sample_ratio=1.0,
                random_seed=self.config['data'].get('random_seed', 42)
            )
        else:
            # 先创建训练集来获取标准化器
            train_dataset = IceDataset(
                data_dir=self.config['data']['data_dir'],
                sequence_length=self.config['data']['sequence_length'],
                prediction_horizon=self.config['data']['prediction_horizon'],
                train_ratio=self.config['data'].get('train_ratio', 0.8),
                mode='train',
                sample_ratio=1.0,
                random_seed=self.config['data'].get('random_seed', 42)
            )
            
            dataset = IceDataset(
                data_dir=self.config['data']['data_dir'],
                sequence_length=self.config['data']['sequence_length'],
                prediction_horizon=self.config['data']['prediction_horizon'],
                train_ratio=self.config['data'].get('train_ratio', 0.8),
                mode='test',
                sample_ratio=1.0,
                random_seed=self.config['data'].get('random_seed', 42),
                shared_scalers=(train_dataset.scaler_weather, train_dataset.scaler_road)
            )
        
        # 提取特征
        weather_features = [
            'weather_air_temp', 'weather_hum', 'weather_pressure',
            'weather_wind_speed', 'weather_wind_direction',
            'weather_rainfall', 'weather_rainfall_intensity'
        ]
        road_features = [
            'road_road_surface_temp', 'road_water_film_height',
            'road_freezing_temp', 'road_saltness'
        ]
        
        weather_data = df[weather_features].values
        road_data = df[road_features].values
        
        # 标准化数据
        weather_data = dataset.scaler_weather.transform(weather_data)
        road_data = dataset.scaler_road.transform(road_data)
        
        # 计算可预测的段数
        seq_len = self.config['data']['sequence_length']
        pred_len = self.config['data']['prediction_horizon']
        max_segments = (len(df) - seq_len) // pred_len
        
        if max_segments <= 0:
            print(f"警告: 文件 {event_file} 长度不足，无法进行预测")
            return df, np.array([]), np.array([])
        
        # 进行分段预测
        all_predictions = []
        prediction_timestamps = []
        
        print(f"对文件 {event_file} 进行分段预测，共 {max_segments} 个段...")
        
        with torch.no_grad():
            for start_idx in range(max_segments):
                # 准备输入数据
                start_idx = start_idx * pred_len
                weather_history = weather_data[start_idx:start_idx + seq_len]
                weather_future = weather_data[start_idx + seq_len:start_idx + seq_len + pred_len]
                road_history = road_data[start_idx:start_idx + seq_len]
                
                # 转换为tensor并添加batch维度
                weather_history = torch.FloatTensor(weather_history).unsqueeze(0).to(self.device)
                weather_future = torch.FloatTensor(weather_future).unsqueeze(0).to(self.device)
                road_history = torch.FloatTensor(road_history).unsqueeze(0).to(self.device)
                
                # 预测
                predictions = self.model(weather_history, weather_future, road_history, pred_len)
                predictions = torch.sigmoid(predictions).cpu().numpy()[0]  # 应用sigmoid并移除batch维度
                
                # 记录预测结果和对应时间戳
                pred_start_time = df.iloc[start_idx + seq_len]['datetime']
                pred_timestamps = [pred_start_time + timedelta(seconds=10*i) for i in range(pred_len)]
                
                all_predictions.append(predictions)
                prediction_timestamps.extend(pred_timestamps)
        
        # 将预测结果组织成数组
        predictions_array = np.array(all_predictions)
        timestamps_array = np.array(prediction_timestamps)
        
        return df, predictions_array, timestamps_array
    
    def plot_single_file_predictions(self, event_file: str, save_dir: str = 'visualizations'):
        """
        绘制单个文件的预测结果
        
        Args:
            event_file: 事件文件名
            save_dir: 保存目录
        """
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 获取预测结果
        df, predictions, timestamps = self.predict_single_file(event_file)
        
        if len(predictions) == 0:
            print(f"跳过文件 {event_file}：数据长度不足")
            return
        
        # 确定文件属于训练集还是测试集
        event_id = event_file.replace('.csv', '')
        dataset_type = "训练集" if event_id in self.train_events else "测试集"
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        fig.suptitle(f'{event_file} - {dataset_type} - 道路结冰预测可视化', fontsize=16)
        
        # 上图：实际结冰情况
        ax1.plot(df['datetime'], df['is_icing'], 'b-', linewidth=2, label='实际结冰状态')
        ax1.fill_between(df['datetime'], 0, df['is_icing'], alpha=0.3, color='blue')
        ax1.set_ylabel('结冰状态', fontsize=12)
        ax1.set_title('实际结冰时段', fontsize=14)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 下图：预测结果热力图
        if len(predictions) > 0:
            # 创建预测热力图数据
            pred_len = self.config['data']['prediction_horizon']
            time_range = pd.date_range(start=df['datetime'].iloc[0], 
                                     end=df['datetime'].iloc[-1], 
                                     freq='10S')
            
            # 创建预测矩阵
            pred_matrix = np.full((len(predictions), pred_len), np.nan)
            for i, pred in enumerate(predictions):
                pred_matrix[i] = pred
            
            # 绘制热力图
            im = ax2.imshow(pred_matrix.T, aspect='auto', cmap='Reds', 
                           vmin=0, vmax=1, origin='lower')
            
            # 设置x轴为时间
            x_ticks = np.linspace(0, len(predictions)-1, min(10, len(predictions)))
            x_labels = [timestamps[int(i*pred_len)].strftime('%H:%M:%S') if int(i*pred_len) < len(timestamps) else '' 
                       for i in x_ticks]
            ax2.set_xticks(x_ticks)
            ax2.set_xticklabels(x_labels, rotation=45)
            
            ax2.set_ylabel('预测时间步', fontsize=12)
            ax2.set_xlabel('预测起始时间', fontsize=12)
            ax2.set_title('预测结冰概率热力图', fontsize=14)
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax2)
            cbar.set_label('结冰概率', fontsize=12)
        
        # 格式化时间轴
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
        
        plt.tight_layout()
        
        # 保存图片
        save_path = os.path.join(save_dir, f'{event_id}_{dataset_type}_prediction.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"已保存可视化结果: {save_path}")
    
    def visualize_all_files(self, save_dir: str = 'visualizations'):
        """
        对所有文件进行可视化
        
        Args:
            save_dir: 保存目录
        """
        # 获取所有CSV文件
        data_dir = self.config['data']['data_dir']
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        csv_files.sort()
        
        print(f"开始处理 {len(csv_files)} 个文件...")
        
        for i, csv_file in enumerate(csv_files, 1):
            print(f"处理文件 {i}/{len(csv_files)}: {csv_file}")
            try:
                self.plot_single_file_predictions(csv_file, save_dir)
            except Exception as e:
                print(f"处理文件 {csv_file} 时出错: {e}")
                continue
        
        print(f"所有文件处理完成，结果保存在: {save_dir}")


def main():
    parser = argparse.ArgumentParser(description='道路结冰预测可视化')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--model', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--file', type=str, help='指定单个文件进行可视化（如 ice_event_001.csv）')
    parser.add_argument('--output', type=str, default='visualizations', help='输出目录')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.config):
        print(f"错误: 配置文件 {args.config} 不存在")
        return
    
    if not os.path.exists(args.model):
        print(f"错误: 模型文件 {args.model} 不存在")
        return
    
    # 创建可视化器
    visualizer = IcePredictionVisualizer(args.config, args.model)
    
    if args.file:
        # 可视化单个文件
        print(f"可视化单个文件: {args.file}")
        visualizer.plot_single_file_predictions(args.file, args.output)
    else:
        # 可视化所有文件
        print("可视化所有文件...")
        visualizer.visualize_all_files(args.output)


if __name__ == "__main__":
    main()
