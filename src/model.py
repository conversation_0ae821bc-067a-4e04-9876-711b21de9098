"""
基于LSTM的Encoder-Decoder模型用于道路结冰预测
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional


class WeatherEncoder(nn.Module):
    """天气数据编码器 - 处理a+b长度的天气数据，输出b长度的天气状态序列"""

    def __init__(self,
                 weather_input_dim: int,
                 hidden_dim: int,
                 num_layers: int = 2,
                 dropout: float = 0.2):
        """
        初始化天气编码器

        Args:
            weather_input_dim: 天气特征维度
            hidden_dim: LSTM隐藏层维度
            num_layers: LSTM层数
            dropout: Dropout概率
        """
        super(WeatherEncoder, self).__init__()

        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        # 处理完整天气序列的LSTM（历史+未来）
        self.weather_lstm = nn.LSTM(
            input_size=weather_input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=True
        )

        # 将双向LSTM输出投影到单向维度
        self.projection = nn.Linear(hidden_dim * 2, hidden_dim)
        self.dropout = nn.Dropout(dropout)

    def forward(self, weather_full_sequence: torch.Tensor, history_length: int) -> torch.Tensor:
        """
        前向传播

        Args:
            weather_full_sequence: 完整天气数据 [batch_size, a+b, weather_features]
            history_length: 历史长度a

        Returns:
            weather_states_b: b长度的天气状态序列 [batch_size, b, hidden_dim]
        """
        # 处理完整天气序列
        lstm_output, _ = self.weather_lstm(weather_full_sequence)  # [batch_size, a+b, hidden_dim*2]

        # 投影到单向维度
        projected_output = self.projection(lstm_output)  # [batch_size, a+b, hidden_dim]
        projected_output = self.dropout(F.relu(projected_output))

        # 提取未来b个时间步的天气状态
        weather_states_b = projected_output[:, history_length:, :]  # [batch_size, b, hidden_dim]

        return weather_states_b


class RoadEncoder(nn.Module):
    """道路数据编码器 - 处理a长度的历史道路数据，输出初始状态"""

    def __init__(self,
                 road_input_dim: int,
                 hidden_dim: int,
                 num_layers: int = 2,
                 dropout: float = 0.2):
        """
        初始化道路编码器

        Args:
            road_input_dim: 道路特征维度
            hidden_dim: LSTM隐藏层维度
            num_layers: LSTM层数
            dropout: Dropout概率
        """
        super(RoadEncoder, self).__init__()

        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        # 道路历史数据LSTM
        self.road_lstm = nn.LSTM(
            input_size=road_input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=False
        )

        self.dropout = nn.Dropout(dropout)

    def forward(self, road_history: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播

        Args:
            road_history: 历史道路数据 [batch_size, a, road_features]

        Returns:
            initial_hidden: 初始隐藏状态 [num_layers, batch_size, hidden_dim]
            initial_cell: 初始细胞状态 [num_layers, batch_size, hidden_dim]
        """
        # 处理道路历史数据
        _, (hidden, cell) = self.road_lstm(road_history)

        return hidden, cell


class IceDecoder(nn.Module):
    """结冰预测解码器 - 迭代预测b个时间步的结冰概率"""

    def __init__(self,
                 weather_state_dim: int,
                 hidden_dim: int,
                 num_layers: int = 2,
                 dropout: float = 0.2):
        """
        初始化解码器

        Args:
            weather_state_dim: 天气状态向量维度
            hidden_dim: LSTM隐藏层维度
            num_layers: LSTM层数
            dropout: Dropout概率
        """
        super(IceDecoder, self).__init__()

        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        # 解码器LSTM，输入是天气状态
        self.decoder_lstm = nn.LSTM(
            input_size=weather_state_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )

        # 预测层
        self.prediction_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()  # 输出概率
        )

        self.dropout = nn.Dropout(dropout)
        
    def forward(self,
                weather_states_b: torch.Tensor,
                initial_hidden: torch.Tensor,
                initial_cell: torch.Tensor) -> torch.Tensor:
        """
        前向传播 - 迭代预测b个时间步

        Args:
            weather_states_b: b长度的天气状态序列 [batch_size, b, weather_state_dim]
            initial_hidden: 初始隐藏状态 [num_layers, batch_size, hidden_dim]
            initial_cell: 初始细胞状态 [num_layers, batch_size, hidden_dim]

        Returns:
            predictions: b长度的结冰预测序列 [batch_size, b]
        """
        batch_size = weather_states_b.size(0)
        prediction_horizon = weather_states_b.size(1)

        predictions = []
        current_hidden = initial_hidden
        current_cell = initial_cell

        # 迭代预测每个时间步
        for t in range(prediction_horizon):
            # 获取当前时刻的天气状态
            current_weather_state = weather_states_b[:, t:t+1, :]  # [batch_size, 1, weather_state_dim]

            # LSTM前向传播
            lstm_output, (current_hidden, current_cell) = self.decoder_lstm(
                current_weather_state, (current_hidden, current_cell)
            )

            # 应用dropout
            lstm_output = self.dropout(lstm_output)

            # 生成当前时刻的预测
            prediction_t = self.prediction_head(lstm_output.squeeze(1))  # [batch_size, 1]
            predictions.append(prediction_t)

        # 合并所有预测
        predictions = torch.cat(predictions, dim=1)  # [batch_size, b]

        return predictions


class IcePredictionModel(nn.Module):
    """完整的道路结冰预测模型"""

    def __init__(self,
                 weather_input_dim: int = 7,
                 road_input_dim: int = 4,
                 hidden_dim: int = 128,
                 num_layers: int = 2,
                 dropout: float = 0.2):
        """
        初始化完整模型

        Args:
            weather_input_dim: 天气特征维度
            road_input_dim: 道路特征维度
            hidden_dim: 隐藏层维度
            num_layers: LSTM层数
            dropout: Dropout概率
        """
        super(IcePredictionModel, self).__init__()

        self.weather_encoder = WeatherEncoder(
            weather_input_dim=weather_input_dim,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            dropout=dropout
        )

        self.road_encoder = RoadEncoder(
            road_input_dim=road_input_dim,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            dropout=dropout
        )

        self.ice_decoder = IceDecoder(
            weather_state_dim=hidden_dim,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            dropout=dropout
        )

    def forward(self,
                weather_history: torch.Tensor,
                weather_future: torch.Tensor,
                road_history: torch.Tensor,
                prediction_horizon: int = 12) -> torch.Tensor:
        """
        前向传播

        Args:
            weather_history: 历史天气数据 [batch_size, a, weather_features]
            weather_future: 未来天气数据 [batch_size, b, weather_features]
            road_history: 历史道路数据 [batch_size, a, road_features]
            prediction_horizon: 预测时间范围 b

        Returns:
            predictions: 预测的结冰概率 [batch_size, b]
        """
        # 合并历史和未来天气数据
        weather_full_sequence = torch.cat([weather_history, weather_future], dim=1)  # [batch_size, a+b, weather_features]
        history_length = weather_history.size(1)

        # 天气编码器：输出b长度的天气状态序列
        weather_states_b = self.weather_encoder(weather_full_sequence, history_length)  # [batch_size, b, hidden_dim]

        # 道路编码器：输出初始状态
        initial_hidden, initial_cell = self.road_encoder(road_history)  # [num_layers, batch_size, hidden_dim]

        # 解码器：迭代预测b个时间步
        predictions = self.ice_decoder(weather_states_b, initial_hidden, initial_cell)  # [batch_size, b]

        return predictions


def create_model(config: dict) -> IcePredictionModel:
    """
    根据配置创建模型
    
    Args:
        config: 模型配置字典
    
    Returns:
        model: 初始化的模型
    """
    model = IcePredictionModel(
        weather_input_dim=config.get('weather_input_dim', 7),
        road_input_dim=config.get('road_input_dim', 4),
        hidden_dim=config.get('hidden_dim', 128),
        num_layers=config.get('num_layers', 2),
        dropout=config.get('dropout', 0.2)
    )
    
    return model


if __name__ == "__main__":
    # 测试模型
    config = {
        'weather_input_dim': 7,
        'road_input_dim': 4,
        'hidden_dim': 128,
        'num_layers': 2,
        'dropout': 0.2
    }
    
    model = create_model(config)
    
    # 创建测试数据
    batch_size = 8
    seq_len = 60
    pred_len = 12
    
    weather_history = torch.randn(batch_size, seq_len, 7)
    weather_future = torch.randn(batch_size, pred_len, 7)
    road_history = torch.randn(batch_size, seq_len, 4)
    
    # 前向传播测试
    with torch.no_grad():
        predictions = model(weather_history, weather_future, road_history, pred_len)
        print(f"模型输出形状: {predictions.shape}")
        print(f"预测值范围: [{predictions.min().item():.4f}, {predictions.max().item():.4f}]")
