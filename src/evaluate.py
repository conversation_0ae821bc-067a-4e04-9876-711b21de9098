"""
模型评估和预测脚本
"""
import os
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
import argparse
import json
from tqdm import tqdm

from data_loader import create_data_loaders
from model import create_model
from utils import load_checkpoint, calculate_metrics, load_config


class Evaluator:
    """模型评估器"""
    
    def __init__(self, config, model_path):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载模型
        self.model = create_model(config['model']).to(self.device)
        checkpoint = load_checkpoint(model_path)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        # 创建数据加载器
        _, self.test_loader = create_data_loaders(
            data_dir=config['data']['data_dir'],
            batch_size=config['training']['batch_size'],
            sequence_length=config['data']['sequence_length'],
            prediction_horizon=config['data']['prediction_horizon'],
            num_workers=config['data']['num_workers'],
            sample_ratio=config['data'].get('sample_ratio', 1.0),
            random_seed=config['data'].get('random_seed', 42),
            train_ratio=config['data'].get('train_ratio', 0.8)
        )
        
        print(f"模型加载完成，设备: {self.device}")
        print(f"测试集批次数: {len(self.test_loader)}")
    
    def evaluate(self):
        """评估模型性能"""
        all_predictions = []
        all_targets = []
        all_event_ids = []
        
        print("开始评估...")
        
        with torch.no_grad():
            for batch in tqdm(self.test_loader, desc='评估进度'):
                # 移动数据到设备
                weather_history = batch['weather_history'].to(self.device)
                weather_future = batch['weather_future'].to(self.device)
                road_history = batch['road_history'].to(self.device)
                targets = batch['target'].to(self.device)
                event_ids = batch['event_id']
                
                # 前向传播
                predictions = self.model(
                    weather_history, 
                    weather_future, 
                    road_history,
                    self.config['data']['prediction_horizon']
                )
                
                # 收集结果
                all_predictions.append(predictions.cpu().numpy())
                all_targets.append(targets.cpu().numpy())
                all_event_ids.extend(event_ids)
        
        # 合并结果
        predictions = np.concatenate(all_predictions, axis=0)
        targets = np.concatenate(all_targets, axis=0)
        
        return predictions, targets, all_event_ids
    
    def calculate_detailed_metrics(self, predictions, targets, threshold=0.5):
        """计算详细的评估指标"""
        # 基本指标
        metrics = calculate_metrics(predictions, targets, threshold)
        
        # 按时间步计算指标
        timestep_metrics = {}
        for t in range(predictions.shape[1]):
            pred_t = predictions[:, t]
            target_t = targets[:, t]
            timestep_metrics[f'timestep_{t+1}'] = calculate_metrics(
                pred_t.reshape(-1, 1), 
                target_t.reshape(-1, 1), 
                threshold
            )
        
        return metrics, timestep_metrics
    
    def plot_confusion_matrix(self, predictions, targets, threshold=0.5, save_path=None):
        """绘制混淆矩阵"""
        pred_flat = predictions.flatten()
        target_flat = targets.flatten()
        
        pred_binary = (pred_flat > threshold).astype(int)
        target_binary = target_flat.astype(int)
        
        cm = confusion_matrix(target_binary, pred_binary)
        
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['No Ice', 'Ice'], 
                   yticklabels=['No Ice', 'Ice'])
        plt.title('Confusion Matrix')
        plt.xlabel('Predicted')
        plt.ylabel('Actual')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_prediction_distribution(self, predictions, targets, save_path=None):
        """绘制预测分布"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 预测概率分布
        axes[0, 0].hist(predictions.flatten(), bins=50, alpha=0.7, color='blue')
        axes[0, 0].set_title('Prediction Probability Distribution')
        axes[0, 0].set_xlabel('Probability')
        axes[0, 0].set_ylabel('Frequency')
        
        # 真实标签分布
        axes[0, 1].hist(targets.flatten(), bins=2, alpha=0.7, color='red')
        axes[0, 1].set_title('True Label Distribution')
        axes[0, 1].set_xlabel('Label')
        axes[0, 1].set_ylabel('Frequency')
        
        # 按时间步的平均预测概率
        timestep_means = np.mean(predictions, axis=0)
        timestep_true_rates = np.mean(targets, axis=0)
        
        x = range(1, len(timestep_means) + 1)
        axes[1, 0].plot(x, timestep_means, 'b-', label='Predicted Probability', marker='o')
        axes[1, 0].plot(x, timestep_true_rates, 'r-', label='True Ice Rate', marker='s')
        axes[1, 0].set_title('Average Prediction by Timestep')
        axes[1, 0].set_xlabel('Timestep')
        axes[1, 0].set_ylabel('Probability/Rate')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # 预测vs真实散点图
        pred_flat = predictions.flatten()
        target_flat = targets.flatten()
        
        # 添加噪声以避免重叠
        target_jittered = target_flat + np.random.normal(0, 0.02, len(target_flat))
        
        axes[1, 1].scatter(target_jittered, pred_flat, alpha=0.1, s=1)
        axes[1, 1].set_title('Predictions vs True Labels')
        axes[1, 1].set_xlabel('True Label (jittered)')
        axes[1, 1].set_ylabel('Predicted Probability')
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_predictions(self, predictions, targets, event_ids, save_dir):
        """保存预测结果"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 创建结果DataFrame
        results = []
        for i, event_id in enumerate(event_ids):
            for t in range(predictions.shape[1]):
                results.append({
                    'event_id': event_id,
                    'timestep': t + 1,
                    'predicted_probability': predictions[i, t],
                    'true_label': targets[i, t],
                    'predicted_label': int(predictions[i, t] > self.config['evaluation']['threshold'])
                })
        
        df = pd.DataFrame(results)
        df.to_csv(os.path.join(save_dir, 'predictions.csv'), index=False)
        
        # 保存汇总统计
        summary = {
            'total_samples': len(predictions),
            'total_timesteps': predictions.shape[1],
            'ice_rate': float(np.mean(targets)),
            'predicted_ice_rate': float(np.mean(predictions > self.config['evaluation']['threshold'])),
            'mean_prediction': float(np.mean(predictions)),
            'std_prediction': float(np.std(predictions))
        }
        
        with open(os.path.join(save_dir, 'summary.json'), 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"预测结果已保存到: {save_dir}")
    
    def run_evaluation(self, save_plots=True, save_predictions=True):
        """运行完整评估"""
        # 获取预测结果
        predictions, targets, event_ids = self.evaluate()
        
        # 计算指标
        overall_metrics, timestep_metrics = self.calculate_detailed_metrics(
            predictions, targets, self.config['evaluation']['threshold']
        )
        
        # 打印结果
        print("\n=== 整体评估指标 ===")
        for metric, value in overall_metrics.items():
            print(f"{metric}: {value:.4f}")
        
        print("\n=== 按时间步评估指标 ===")
        for timestep, metrics in timestep_metrics.items():
            print(f"\n{timestep}:")
            for metric, value in metrics.items():
                print(f"  {metric}: {value:.4f}")
        
        # 保存结果
        if save_predictions and self.config['evaluation']['save_predictions']:
            self.save_predictions(
                predictions, targets, event_ids, 
                self.config['evaluation']['prediction_dir']
            )
        
        # 绘制图表
        if save_plots:
            plot_dir = os.path.join(self.config['evaluation']['prediction_dir'], 'plots')
            os.makedirs(plot_dir, exist_ok=True)
            
            self.plot_confusion_matrix(
                predictions, targets, 
                self.config['evaluation']['threshold'],
                os.path.join(plot_dir, 'confusion_matrix.png')
            )
            
            self.plot_prediction_distribution(
                predictions, targets,
                os.path.join(plot_dir, 'prediction_distribution.png')
            )
        
        return overall_metrics, timestep_metrics, predictions, targets


def main():
    parser = argparse.ArgumentParser(description='评估道路结冰预测模型')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--model', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--threshold', type=float, default=None, help='分类阈值')
    parser.add_argument('--no-plots', action='store_true', help='不生成图表')
    parser.add_argument('--no-save', action='store_true', help='不保存预测结果')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 更新阈值（如果指定）
    if args.threshold is not None:
        config['evaluation']['threshold'] = args.threshold
    
    # 创建评估器
    evaluator = Evaluator(config, args.model)
    
    # 运行评估
    evaluator.run_evaluation(
        save_plots=not args.no_plots,
        save_predictions=not args.no_save
    )


if __name__ == "__main__":
    main()
