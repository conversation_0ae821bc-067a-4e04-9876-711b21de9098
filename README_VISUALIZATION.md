# 道路结冰预测可视化工具

本文档介绍如何使用道路结冰预测系统的可视化工具来分析模型预测结果。

## 可视化脚本概述

项目提供了两个主要的可视化脚本：

1. **`visualize_predictions.py`** - 详细的预测结果可视化
2. **`visualize_ice_periods.py`** - 结冰时段对比可视化

## 前置条件

在使用可视化工具之前，请确保：

1. 已经训练好模型并保存了检查点文件
2. 配置文件 `config.json` 存在且正确
3. 数据文件在指定的目录中

## 1. 详细预测结果可视化 (`visualize_predictions.py`)

### 功能特点

- 根据 `sequence_length` 和 `prediction_horizon` 对数据进行分段预测
- 生成预测概率热力图
- 显示实际结冰状态时间序列
- 自动标注文件属于训练集还是测试集

### 使用方法

#### 可视化单个文件

```bash
python visualize_predictions.py --model checkpoints/best_model.pth --file ice_event_001.csv
```

#### 可视化所有文件

```bash
python visualize_predictions.py --model checkpoints/best_model.pth
```

#### 自定义输出目录

```bash
python visualize_predictions.py --model checkpoints/best_model.pth --output my_visualizations
```

### 输出说明

每个文件会生成一个包含两个子图的可视化：

- **上图**: 实际结冰状态时间序列
- **下图**: 预测结冰概率热力图
  - X轴：预测起始时间
  - Y轴：预测时间步（未来1-N步）
  - 颜色：结冰概率（0-1，红色越深概率越高）

文件命名格式：`{事件ID}_{数据集类型}_prediction.png`

例如：`ice_event_001_训练集_prediction.png`

## 2. 结冰时段对比可视化 (`visualize_ice_periods.py`)

### 功能特点

- 提取实际结冰时段和预测结冰时段
- 以时间轴形式直观对比两者
- 支持自定义预测阈值
- 显示结冰时长统计信息

### 使用方法

#### 可视化单个文件（默认阈值0.5）

```bash
python visualize_ice_periods.py --model checkpoints/best_model.pth --file ice_event_001.csv
```

#### 使用自定义阈值

```bash
python visualize_ice_periods.py --model checkpoints/best_model.pth --file ice_event_001.csv --threshold 0.3
```

#### 可视化所有文件

```bash
python visualize_ice_periods.py --model checkpoints/best_model.pth --threshold 0.4
```

#### 自定义输出目录

```bash
python visualize_ice_periods.py --model checkpoints/best_model.pth --output ice_analysis --threshold 0.6
```

### 输出说明

每个文件会生成一个时间轴对比图：

- **蓝色条带**: 实际结冰时段
- **红色条带**: 预测结冰时段
- **统计信息**: 显示实际和预测的结冰时长

文件命名格式：`{事件ID}_{数据集类型}_ice_periods.png`

例如：`ice_event_001_测试集_ice_periods.png`

## 参数说明

### 通用参数

- `--config`: 配置文件路径（默认：`config.json`）
- `--model`: 模型检查点路径（必需）
- `--file`: 指定单个文件进行可视化（可选，不指定则处理所有文件）
- `--output`: 输出目录（默认：`visualizations` 或 `ice_period_visualizations`）

### 结冰时段可视化特有参数

- `--threshold`: 结冰预测阈值（默认：0.5）

## 预测机制说明

### 分段预测策略

1. **滑动窗口**: 使用长度为 `sequence_length` 的历史数据作为输入
2. **未来信息**: 使用长度为 `prediction_horizon` 的未来天气数据
3. **重叠预测**: 每个时间点可能被多次预测，取平均值
4. **边界处理**: 数据两端缺少足够历史或未来信息的部分会被舍弃

### 数据集划分

- 脚本会自动识别每个事件文件属于训练集还是测试集
- 划分方式与训练时完全一致，确保结果的可比性
- 使用相同的标准化器，保证数据预处理的一致性

## 输出文件组织

```
visualizations/                    # 详细预测结果
├── ice_event_001_训练集_prediction.png
├── ice_event_002_测试集_prediction.png
└── ...

ice_period_visualizations/         # 结冰时段对比
├── ice_event_001_训练集_ice_periods.png
├── ice_event_002_测试集_ice_periods.png
└── ...
```

## 使用建议

### 1. 模型性能分析

- 先使用结冰时段可视化快速了解模型整体表现
- 对于表现异常的文件，使用详细预测可视化深入分析

### 2. 阈值调优

- 尝试不同的预测阈值（如0.3, 0.4, 0.5, 0.6, 0.7）
- 观察哪个阈值能更好地平衡精确率和召回率

### 3. 训练集vs测试集对比

- 比较模型在训练集和测试集上的表现差异
- 识别可能的过拟合问题

### 4. 时间模式分析

- 观察结冰事件的时间分布特征
- 分析模型是否能够捕捉到时间相关的模式

## 故障排除

### 常见问题

1. **内存不足**: 如果处理大文件时内存不足，可以先处理单个文件
2. **模型文件不存在**: 确保模型路径正确，文件存在
3. **数据格式错误**: 确保CSV文件格式与训练时一致

### 调试建议

- 先用单个小文件测试脚本是否正常工作
- 检查配置文件中的参数设置是否正确
- 确保模型检查点文件完整且未损坏

## 扩展功能

可以根据需要对脚本进行扩展：

1. **添加更多统计指标**: 如精确率、召回率、F1分数等
2. **交互式可视化**: 使用plotly等工具创建交互式图表
3. **批量分析**: 生成汇总报告，比较所有文件的性能
4. **实时可视化**: 支持实时数据流的可视化

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 提交Pull Request
- 发送邮件
